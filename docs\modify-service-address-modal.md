# 修改服务地址功能实现文档（弹窗版本）

## 功能概述

根据后台新增的接口 `PUT /orders/{orderId}/updateServiceAddress`，实现了订单服务地址修改功能。采用弹窗式设计，参考了您提供的UI布局，并在此基础上增加了选择已保存地址的功能。

## 设计特点

### 1. 弹窗式设计
- **参考布局**：采用您提供的弹窗布局设计
- **居中显示**：弹窗在屏幕中央显示，背景半透明遮罩
- **响应式尺寸**：弹窗宽度680rpx，最大高度80vh，适配不同屏幕

### 2. 双重地址选择方式
- **获取已保存位置**：从用户地址列表中选择
- **地图选择**：使用微信地图API选择位置
- **切换按钮**：蓝色按钮样式，支持状态切换

### 3. 完整的表单功能
- **服务地址**：支持手动输入和自动填充
- **详细地址**：必填字段，支持字符计数
- **地址备注**：可选字段，支持多行输入

## 技术实现

### 1. 弹窗触发
在订单详情页面点击"修改"按钮时：
```javascript
modifyServiceAddress() {
  // 权限检查
  if (!this.canModifyServiceAddress()) {
    wx.showToast({ title: '当前订单状态不允许修改服务地址', icon: 'none' });
    return;
  }
  
  // 显示弹窗并初始化数据
  this.setData({
    showAddressModal: true,
    addressForm: { /* 当前地址数据 */ }
  });
  
  // 加载用户地址列表
  this.loadUserAddressList();
}
```

### 2. 地址选择逻辑
- **已保存地址**：从`userAddressList`中选择，自动填充表单
- **地图选择**：调用`wx.chooseLocation()`，获取位置信息
- **状态管理**：通过`selectedAddressType`控制显示模式

### 3. 表单验证
```javascript
// 必填字段验证
if (!addressForm.address.trim()) {
  wx.showToast({ title: '请输入服务地址', icon: 'none' });
  return;
}

if (!addressForm.addressDetail.trim()) {
  wx.showToast({ title: '请输入详细地址', icon: 'none' });
  return;
}
```

### 4. 数据提交
```javascript
const submitData = {
  address: addressForm.address.trim(),
  addressDetail: addressForm.addressDetail.trim(),
  longitude: addressForm.longitude,
  latitude: addressForm.latitude,
  addressRemark: addressForm.addressRemark.trim(),
  addressId: addressForm.addressId,
  userType: 'customer'
};

await orderApi.updateServiceAddress(orderDetail.id, submitData);
```

## 界面设计

### 1. 弹窗结构
```
┌─────────────────────────────────┐
│ 修改服务地址                 × │
├─────────────────────────────────┤
│ 选择位置                        │
│ [获取已保存位置] [地图选择]     │
├─────────────────────────────────┤
│ 已保存地址列表 / 地图选择按钮   │
├─────────────────────────────────┤
│ 服务地址: [输入框]              │
│ 详细地址: [输入框]         0/255│
│ 地址备注: [文本域]         0/255│
├─────────────────────────────────┤
│           [取消] [确认修改]     │
└─────────────────────────────────┘
```

### 2. 样式特点
- **圆角设计**：弹窗圆角20rpx，按钮圆角40rpx
- **蓝色主题**：主要操作按钮使用#007aff蓝色
- **状态反馈**：选中状态、加载状态的视觉反馈
- **字符计数**：实时显示输入字符数量

### 3. 交互体验
- **点击遮罩关闭**：点击弹窗外部区域关闭弹窗
- **按钮状态**：加载时按钮变灰并显示"修改中..."
- **滚动支持**：地址列表支持滚动，表单区域支持滚动

## 功能特性

### 1. 权限控制
- **允许修改状态**：待付款、待接单、待服务
- **禁止修改状态**：已出发、服务中、已完成等
- **实时检查**：每次点击修改按钮时检查权限

### 2. 地址管理
- **显示已保存地址**：包含地址名称、详细地址、默认标识
- **空状态处理**：无保存地址时显示提示信息
- **地址选择**：点击地址项自动填充表单

### 3. 地图集成
- **微信地图API**：使用`wx.chooseLocation()`
- **位置信息**：获取地址、名称、经纬度
- **错误处理**：处理用户取消选择的情况

### 4. 数据同步
- **自动刷新**：修改成功后自动刷新订单详情
- **状态更新**：更新页面中的地址显示和修改权限
- **错误处理**：网络错误时的友好提示

## 优势对比

### 相比独立页面的优势：
1. **操作便捷**：无需页面跳转，在当前页面完成修改
2. **上下文保持**：用户可以看到当前订单信息
3. **响应速度**：无页面切换，响应更快
4. **用户体验**：符合移动端操作习惯

### 相比原始设计的增强：
1. **已保存地址**：支持选择用户已保存的地址
2. **双重选择**：既支持地址列表选择，也支持地图选择
3. **完整验证**：前端表单验证确保数据完整性
4. **状态管理**：完善的加载状态和错误处理

## 扩展性

### 1. 功能扩展
- 支持地址搜索和智能推荐
- 支持常用地址快速选择
- 支持地址模板功能

### 2. 样式扩展
- 支持深色模式适配
- 支持自定义主题色
- 支持动画效果增强

### 3. 权限扩展
- 支持管理端和员工端权限
- 支持批量修改功能
- 支持修改历史记录

## 测试建议

1. **功能测试**：测试不同订单状态下的权限控制
2. **交互测试**：测试地址选择、表单输入、提交流程
3. **兼容性测试**：测试不同设备和微信版本的兼容性
4. **网络测试**：测试网络异常情况的处理
5. **用户体验测试**：测试操作流程的流畅性和友好性
